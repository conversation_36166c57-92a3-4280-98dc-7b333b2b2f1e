{"name": "moments_forward", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@runwayml/sdk": "^2.8.0", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.539.0", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "tailwindcss": "^4", "typescript": "^5"}}