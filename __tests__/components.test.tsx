import { render, screen, fireEvent } from '@testing-library/react'
import { useAppStore } from '@/stores/appStore'
import StoryContext from '@/components/StoryContext'
import ProgressIndicator from '@/components/ProgressIndicator'

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    header: ({ children, ...props }: any) => <header {...props}>{children}</header>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    path: ({ children, ...props }: any) => <path {...props}>{children}</path>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, ...props }: any) => <img src={src} alt={alt} {...props} />,
}))

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Lock: () => <div data-testid="lock-icon">Lock</div>,
  CheckCircle: () => <div data-testid="check-icon">Check</div>,
  Camera: () => <div data-testid="camera-icon">Camera</div>,
  Send: () => <div data-testid="send-icon">Send</div>,
  Sparkles: () => <div data-testid="sparkles-icon">Sparkles</div>,
  Heart: () => <div data-testid="heart-icon">Heart</div>,
}))

describe('App Store', () => {
  beforeEach(() => {
    // Reset store state before each test
    useAppStore.getState().initializePhotos([
      {
        id: '1',
        src: '/photos/photo_1.jpg',
        alt: 'Test photo 1',
        date: 'Summer 2010',
        location: 'Test location',
        isUnlocked: true,
        contextStory: 'Test story',
        brotherSignature: 'Test signature'
      },
      {
        id: '2',
        src: '/photos/photo_2.jpg',
        alt: 'Test photo 2',
        date: 'Winter 2011',
        location: 'Test location 2',
        isUnlocked: false,
        contextStory: 'Test story 2',
        brotherSignature: 'Test signature 2'
      }
    ])
  })

  it('calculates progress correctly', () => {
    const store = useAppStore.getState()
    const progress = store.getTotalProgress()
    expect(progress).toBe(50) // 1 of 2 photos unlocked
  })

  it('unlocks next photo correctly', () => {
    const store = useAppStore.getState()
    store.unlockNextPhoto()

    const photos = store.photos
    expect(photos[1].isUnlocked).toBe(true)
    expect(store.currentPhotoIndex).toBe(1)
  })
})

describe('StoryContext Component', () => {
  it('renders story and signature', () => {
    render(
      <StoryContext
        story="This is a test story"
        signature="- Your loving brother ❤️"
        isVisible={true}
      />
    )

    // Check for individual words since the story is split into spans
    expect(screen.getByText('This')).toBeInTheDocument()
    expect(screen.getByText('test')).toBeInTheDocument()
    expect(screen.getByText('story')).toBeInTheDocument()
    expect(screen.getByText('- Your loving brother ❤️')).toBeInTheDocument()
  })
})

describe('Utility Functions', () => {
  it('validates prompts correctly', () => {
    const { validatePrompt } = require('@/lib/utils')

    // Test short prompt
    const shortResult = validatePrompt('Short', 20)
    expect(shortResult.isValid).toBe(false)
    expect(shortResult.error).toContain('at least 20 characters')

    // Test valid prompt
    const validResult = validatePrompt('This is a valid prompt that is long enough', 20)
    expect(validResult.isValid).toBe(true)
    expect(validResult.error).toBeUndefined()

    // Test empty prompt
    const emptyResult = validatePrompt('', 20)
    expect(emptyResult.isValid).toBe(false)
    expect(emptyResult.error).toBe('Please enter a prompt')
  })
})

describe('ProgressIndicator Component', () => {
  beforeEach(() => {
    // Reset the store before each test
    useAppStore.getState().initializePhotos([
      {
        id: '1',
        src: '/photos/photo_1.jpg',
        alt: 'Test photo 1',
        date: 'Summer 2010',
        location: 'Test location',
        isUnlocked: true,
        contextStory: 'Test story',
        brotherSignature: 'Test signature'
      },
      {
        id: '2',
        src: '/photos/photo_2.jpg',
        alt: 'Test photo 2',
        date: 'Winter 2011',
        location: 'Test location 2',
        isUnlocked: false,
        contextStory: 'Test story 2',
        brotherSignature: 'Test signature 2'
      }
    ])
  })

  it('renders progress indicator with correct progress', () => {
    render(<ProgressIndicator />)
    
    // Should show 50% progress (1 of 2 photos unlocked)
    expect(screen.getByText('50%')).toBeInTheDocument()
    expect(screen.getByText('1 of 2')).toBeInTheDocument()
    expect(screen.getByText('memories unlocked')).toBeInTheDocument()
  })
})
