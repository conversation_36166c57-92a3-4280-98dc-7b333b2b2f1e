# RunwayML Integration Guide

This document provides a comprehensive guide for the RunwayML Gen-4 Turbo integration in the Moments Forward application.

## Overview

The integration includes:
- ✅ Official RunwayML SDK (@runwayml/sdk)
- ✅ Gen-4 Turbo model support
- ✅ Image-to-video generation
- ✅ Proper error handling and status polling
- ✅ Mock service for development
- ✅ TypeScript support with proper types
- ✅ Comprehensive test coverage

## Setup

### 1. Environment Configuration

Copy the example environment file and add your RunwayML API key:

```bash
cp .env.example .env.local
```

Edit `.env.local` and add your API key:

```env
# Get your API key from: https://app.runwayml.com/account/api-keys
RUNWAY_API_KEY=your_runway_api_key_here
RUNWAY_BASE_URL=https://api.dev.runwayml.com
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

### 2. Install Dependencies

The RunwayML SDK is already installed. If you need to reinstall:

```bash
npm install @runwayml/sdk
```

## Usage

### Basic Video Generation

```typescript
import { createRunwayMLService, createImageToVideoRequest } from '@/lib/runwayml';

// Create service instance
const runwayService = createRunwayMLService();

// Create request
const request = createImageToVideoRequest(
  'https://example.com/image.jpg',
  'A beautiful landscape with flowing water',
  {
    duration: 5,
    ratio: '1280:720',
    seed: 12345,
  }
);

// Generate video and wait for completion
const result = await runwayService.generateVideoAndWait(request);
console.log('Video URL:', result.videoUrl);
```

### Using the API Endpoint

```typescript
// POST /api/generate-video
const response = await fetch('/api/generate-video', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    imageUrl: 'https://example.com/image.jpg',
    prompt: 'A beautiful landscape with flowing water',
    duration: 5,
    ratio: '1280:720',
    seed: 12345,
  }),
});

const data = await response.json();
if (data.success) {
  console.log('Video URL:', data.videoUrl);
  console.log('Task ID:', data.generationId);
}
```

### Status Polling

```typescript
// GET /api/generate-video?id=task_id
const statusResponse = await fetch(`/api/generate-video?id=${taskId}`);
const status = await statusResponse.json();

console.log('Status:', status.status);
console.log('Progress:', status.progress);
if (status.status === 'completed') {
  console.log('Video URL:', status.videoUrl);
}
```

### Using React Hooks

```typescript
import { useVideoGeneration, useRunwayML } from '@/hooks/useVideoGeneration';

function VideoGenerator() {
  const { generateVideo, isGenerating } = useVideoGeneration();
  const { generateVideoWithRunway, progress } = useRunwayML();

  const handleGenerate = async () => {
    // Using the app's video generation hook
    const videoUrl = await generateVideo('photo1', 'A beautiful scene', {
      duration: 5,
      ratio: '1280:720',
    });

    // Or using RunwayML directly
    const { videoUrl: directUrl } = await generateVideoWithRunway(
      'https://example.com/image.jpg',
      'A beautiful scene',
      { pollForCompletion: true }
    );
  };

  return (
    <div>
      <button onClick={handleGenerate} disabled={isGenerating}>
        {isGenerating ? `Generating... ${progress}%` : 'Generate Video'}
      </button>
    </div>
  );
}
```

## Configuration Options

### Model Parameters

- **model**: `'gen4_turbo'` (only supported model)
- **duration**: `5 | 10` (seconds)
- **ratio**: Supported aspect ratios:
  - `'1280:720'` (16:9 landscape)
  - `'720:1280'` (9:16 portrait)
  - `'1104:832'` (4:3 landscape)
  - `'832:1104'` (3:4 portrait)
  - `'960:960'` (1:1 square)
  - `'1584:672'` (21:9 ultrawide)
  - `'1280:768'` (5:3 landscape)
  - `'768:1280'` (3:5 portrait)
- **seed**: Optional number for reproducible results

### Service Configuration

The service automatically detects the environment:
- **With API key**: Uses real RunwayML API
- **Without API key**: Uses mock service for development

## Development Mode

Without a RunwayML API key, the application uses a mock service that:
- Simulates API delays
- Returns mock video URLs
- Provides realistic response structures
- Allows full UI testing

## Error Handling

The integration includes comprehensive error handling:

```typescript
try {
  const result = await runwayService.generateVideoAndWait(request);
  console.log('Success:', result.videoUrl);
} catch (error) {
  if (error instanceof TaskFailedError) {
    console.error('Generation failed:', error.message);
  } else {
    console.error('Service error:', error.message);
  }
}
```

## Testing

Run the test suite:

```bash
# Run all RunwayML tests
npm test -- src/__tests__/runwayml.test.ts

# Run all tests
npm test
```

## API Reference

### Functions

- `createRunwayMLService()`: Creates service instance
- `prepareImageForRunway(url)`: Prepares image URL for API
- `optimizePromptForVideo(prompt)`: Optimizes text prompt for video generation
- `createImageToVideoRequest(image, prompt, options)`: Creates API request
- `imageToDataUri(base64, mimeType)`: Converts base64 to data URI

### Types

- `ImageToVideoRequest`: Request parameters
- `GenerationResponse`: API response format
- `RunwayMLConfig`: Service configuration

## Image Handling

The integration automatically handles different image formats:

### HTTPS URLs
- Used directly with RunwayML API
- No conversion needed

### HTTP URLs and Local Files
- Automatically converted to base64 data URIs
- Supports JPEG, PNG, GIF, WebP, and SVG formats
- Handles local development images seamlessly

### Data URIs
- Used directly if already in data URI format
- Supports all standard image MIME types

## Troubleshooting

### Common Issues

1. **"RUNWAY_API_KEY not found"**: Add your API key to `.env.local`
2. **"Only HTTPS URLs are allowed"**: Fixed automatically by converting to data URI
3. **"Video generation failed"**: Check image URL accessibility and prompt length
4. **"Generation timed out"**: Increase timeout or check API status
5. **"Invalid duration"**: Use only 5 or 10 seconds for Gen-4 Turbo
6. **"File not found"**: Ensure image files exist in the public directory

### Debug Mode

Enable debug logging by setting:

```env
NODE_ENV=development
```

## Production Deployment

1. Set environment variables in your deployment platform
2. Ensure `RUNWAY_API_KEY` is properly configured
3. Set `RUNWAY_BASE_URL=https://api.dev.runwayml.com`
4. Configure `NEXT_PUBLIC_BASE_URL` to your domain

## Cost Considerations

Gen-4 Turbo pricing:
- 5 credits per second of video
- 5-second video = 25 credits
- 10-second video = 50 credits

Monitor usage through the RunwayML dashboard.

## Support

For issues with the integration:
1. Check the console for error messages
2. Verify API key and environment configuration
3. Test with the mock service first
4. Review the RunwayML API documentation

For RunwayML API issues:
- Visit: https://docs.dev.runwayml.com/
- Support: https://runwayml.com/support/
