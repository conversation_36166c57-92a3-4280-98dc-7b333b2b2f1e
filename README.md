# Moments Forward

An interactive photo timeline web application that transforms childhood memories into AI-generated video continuations. Users (siblings) collaboratively unlock a chronological sequence of family photos by writing story prompts that imagine what happened in the next 10 seconds after each photo was taken.

## Features

- **Interactive Photo Timeline**: Horizontal/vertical responsive timeline with lock/unlock progression
- **Story Context**: Brother's pre-written stories for each photo with handwritten styling
- **AI Video Generation**: Integration with RunwayML API to generate 10-second videos from prompts
- **Vintage Aesthetic**: Warm color palette, film grain effects, and nostalgic design elements
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Smooth Animations**: Framer Motion powered transitions and loading states

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS v4 with custom design system
- **Animations**: Framer Motion
- **State Management**: Zustand
- **Icons**: Lucide React
- **Fonts**: Google Fonts (Playfair Display, Inter, Kalam, Poppins)
- **AI Integration**: RunwayML API

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- RunwayML API key (optional for development)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd moments_forward
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Edit `.env.local` and add your RunwayML API key:
```
RUNWAY_API_KEY=your_runway_api_key_here
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/                 # Next.js app directory
│   ├── api/            # API routes
│   ├── globals.css     # Global styles and design system
│   ├── layout.tsx      # Root layout with fonts
│   └── page.tsx        # Main application page
├── components/         # React components
│   ├── Timeline.tsx    # Photo timeline component
│   ├── PhotoStage.tsx  # Main photo display
│   ├── StoryContext.tsx # Brother's story display
│   ├── InputComponent.tsx # Prompt input interface
│   ├── VideoPlayer.tsx # Custom video player
│   └── LoadingAnimation.tsx # Loading states
├── hooks/              # Custom React hooks
├── lib/                # Utility functions and services
├── stores/             # Zustand state management
└── types/              # TypeScript type definitions
```

## Design System

### Colors
- **Vintage Sepia**: `#F4E4BC` - Primary background
- **Vintage Gold**: `#D4AF37` - Accent and highlights
- **Vintage Cream**: `#FFF8DC` - Secondary background
- **Vintage Brown**: `#3C2415` - Text and borders
- **Vintage Off-White**: `#FEFDF9` - Main background

### Typography
- **Headers**: Playfair Display (elegant serif)
- **Body**: Inter (clean sans-serif)
- **Handwritten**: Kalam (personal feel)
- **UI Elements**: Poppins (modern, friendly)

### Spacing
8px base unit system: 8, 16, 24, 32, 48, 64px

## API Integration

### RunwayML Setup

1. Sign up for RunwayML API access
2. Get your API key from the RunwayML dashboard
3. Add the key to your `.env.local` file
4. The app will automatically use the API for video generation

### Mock Mode

For development without RunwayML API:
- The app includes mock video generation
- Placeholder videos are shown instead of real generations
- All UI flows work the same way

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- AWS Amplify
- Self-hosted with Docker

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
