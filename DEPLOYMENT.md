# Deployment Guide

This guide covers different deployment options for the Moments Forward application.

## Prerequisites

- Node.js 18+ installed
- RunwayML API key (optional for development)
- Git repository set up

## Environment Variables

Create a `.env.local` file with the following variables:

```bash
# Required for production
RUNWAY_API_KEY=your_runway_api_key_here
RUNWAY_BASE_URL=https://api.runwayml.com/v1
NEXT_PUBLIC_BASE_URL=https://your-domain.com

# Optional
DATABASE_URL=your_database_url_here
```

## Deployment Options

### 1. Vercel (Recommended)

Vercel provides the easiest deployment for Next.js applications.

#### Quick Deploy

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/moments-forward)

#### Manual Setup

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy**
   ```bash
   vercel --prod
   ```

4. **Set Environment Variables**
   - Go to your Vercel dashboard
   - Navigate to your project settings
   - Add environment variables:
     - `RUNWAY_API_KEY`
     - `RUNWAY_BASE_URL`
     - `NEXT_PUBLIC_BASE_URL`

#### GitHub Integration

1. Connect your GitHub repository to Vercel
2. Set up automatic deployments on push to main branch
3. Configure environment variables in Vercel dashboard

### 2. Docker Deployment

#### Build and Run Locally

```bash
# Build the Docker image
docker build -t moments-forward .

# Run the container
docker run -p 3000:3000 \
  -e RUNWAY_API_KEY=your_api_key \
  -e RUNWAY_BASE_URL=https://api.runwayml.com/v1 \
  -e NEXT_PUBLIC_BASE_URL=http://localhost:3000 \
  moments-forward
```

#### Using Docker Compose

```bash
# Create .env file with your variables
cp .env.example .env

# Start the application
docker-compose up -d

# With production profile (includes nginx)
docker-compose --profile production up -d
```

### 3. Traditional VPS/Server

#### Prerequisites

- Ubuntu 20.04+ or similar Linux distribution
- Node.js 18+ installed
- PM2 for process management
- Nginx for reverse proxy (optional)

#### Setup Steps

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/moments-forward.git
   cd moments-forward
   ```

2. **Install dependencies**
   ```bash
   npm ci
   ```

3. **Build the application**
   ```bash
   npm run build
   ```

4. **Install PM2**
   ```bash
   npm install -g pm2
   ```

5. **Create PM2 ecosystem file**
   ```javascript
   // ecosystem.config.js
   module.exports = {
     apps: [{
       name: 'moments-forward',
       script: 'npm',
       args: 'start',
       env: {
         NODE_ENV: 'production',
         PORT: 3000,
         RUNWAY_API_KEY: 'your_api_key',
         RUNWAY_BASE_URL: 'https://api.runwayml.com/v1',
         NEXT_PUBLIC_BASE_URL: 'https://your-domain.com'
       }
     }]
   }
   ```

6. **Start with PM2**
   ```bash
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

#### Nginx Configuration (Optional)

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 4. Railway

1. **Install Railway CLI**
   ```bash
   npm install -g @railway/cli
   ```

2. **Login and deploy**
   ```bash
   railway login
   railway init
   railway up
   ```

3. **Set environment variables**
   ```bash
   railway variables set RUNWAY_API_KEY=your_api_key
   railway variables set RUNWAY_BASE_URL=https://api.runwayml.com/v1
   ```

### 5. Netlify

1. **Build command**: `npm run build`
2. **Publish directory**: `.next`
3. **Set environment variables** in Netlify dashboard

## Post-Deployment Checklist

- [ ] Verify all environment variables are set
- [ ] Test RunwayML API integration
- [ ] Check responsive design on mobile devices
- [ ] Verify SSL certificate is working
- [ ] Test photo upload and video generation flow
- [ ] Monitor application performance
- [ ] Set up error tracking (Sentry, LogRocket, etc.)
- [ ] Configure analytics (Google Analytics, Plausible, etc.)

## Monitoring and Maintenance

### Health Checks

The application includes a health check endpoint at `/api/health`:

```bash
curl https://your-domain.com/api/health
```

### Logs

- **Vercel**: Check function logs in Vercel dashboard
- **Docker**: `docker logs container_name`
- **PM2**: `pm2 logs moments-forward`

### Updates

1. Pull latest changes
2. Install dependencies: `npm ci`
3. Build: `npm run build`
4. Restart application

## Troubleshooting

### Common Issues

1. **Build failures**: Check Node.js version compatibility
2. **API errors**: Verify RunwayML API key and permissions
3. **Image loading issues**: Check image domains in next.config.ts
4. **Performance issues**: Enable compression and optimize images

### Support

For deployment issues, check:
- Application logs
- Network connectivity
- Environment variable configuration
- API rate limits
