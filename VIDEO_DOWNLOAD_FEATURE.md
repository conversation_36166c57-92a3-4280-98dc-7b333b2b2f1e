# Video Download Feature

## Overview
This feature automatically downloads and saves generated videos from the Runway API locally to prevent loss of expensive API-generated content.

## How It Works

### 1. Video Generation Flow
1. User submits a prompt for video generation
2. API calls Runway ML to generate video
3. **NEW**: Once video is generated, it's automatically downloaded and saved locally
4. Both original Runway URL and local path are stored
5. Frontend preferentially uses local path for playback

### 2. Local Storage
- Videos are saved to: `public/generated-videos/`
- Filename format: `runway_{taskId}_{timestamp}.mp4`
- Local videos are served via Next.js static file serving

### 3. Download Button
- Added download button to video player controls
- Users can download videos to their device
- Downloads use the local video file when available

## File Structure
```
public/
  generated-videos/          # Auto-created directory
    runway_abc123_1234567.mp4 # Downloaded videos
    mock_test.mp4            # Test file for development
```

## API Changes

### Response Format
```json
{
  "success": true,
  "videoUrl": "/generated-videos/runway_abc123_1234567.mp4", // Local path (preferred)
  "originalVideoUrl": "https://runway.com/video/abc123", // Original Runway URL
  "localVideoPath": "/generated-videos/runway_abc123_1234567.mp4"
}
```

### Database Schema Updates
The `VideoGeneration` type now includes:
- `originalVideoUrl`: Original Runway video URL
- `localVideoPath`: Local saved video path
- `videoUrl`: Preferred video URL (local if available)

## Error Handling
- If video download fails, the original Runway URL is still available
- Download failures are logged but don't fail the entire generation request
- Frontend gracefully falls back to original URL if local file is missing

## Development Notes
- Videos are gitignored to avoid committing large files
- Mock service provides test video paths for development
- Video player component handles both local and remote URLs

## Benefits
1. **Cost Savings**: Prevents loss of expensive API-generated videos
2. **Performance**: Local videos load faster than remote URLs
3. **Reliability**: Videos remain available even if Runway URLs expire
4. **User Experience**: Download button allows users to save videos locally
