// Mock the RunwayML SDK first
jest.mock('@runwayml/sdk', () => ({
  __esModule: true,
  default: jest.fn(),
  TaskFailedError: class TaskFailedError extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'TaskFailedError';
    }
  },
}));

// Mock the RunwayML service
jest.mock('@/lib/runwayml', () => ({
  createRunwayMLService: jest.fn(() => ({
    generateVideoAndWait: jest.fn(),
    getGenerationStatus: jest.fn(),
  })),
  prepareImageForRunway: jest.fn((url) => Promise.resolve(url)),
  createImageToVideoRequest: jest.fn((image, prompt, options) => ({
    model: 'gen4_turbo',
    promptImage: image,
    promptText: prompt,
    ...options,
  })),
}));

// Mock NextRequest to avoid edge runtime issues in tests
const createMockRequest = (url: string, options: any = {}) => ({
  json: jest.fn().mockResolvedValue(options.body ? JSON.parse(options.body) : {}),
  url,
  method: options.method || 'GET',
});

import { POST, GET } from '@/app/api/generate-video/route';

const mockRunwayService = {
  generateVideoAndWait: jest.fn(),
  getGenerationStatus: jest.fn(),
};

const { createRunwayMLService } = require('@/lib/runwayml');
createRunwayMLService.mockReturnValue(mockRunwayService);

describe('/api/generate-video', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST', () => {
    it('should generate video successfully', async () => {
      const mockResponse = {
        id: 'test-task-id',
        status: 'completed',
        videoUrl: 'https://example.com/video.mp4',
        createdAt: '2024-01-01T00:00:00Z',
        completedAt: '2024-01-01T00:01:00Z',
      };

      mockRunwayService.generateVideoAndWait.mockResolvedValue(mockResponse);

      const request = createMockRequest('http://localhost:3000/api/generate-video', {
        method: 'POST',
        body: JSON.stringify({
          imageUrl: 'https://example.com/image.jpg',
          prompt: 'A beautiful landscape with flowing water',
        }),
      });

      const response = await POST(request as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({
        success: true,
        generationId: 'test-task-id',
        status: 'completed',
        videoUrl: 'https://example.com/video.mp4',
        progress: undefined,
        createdAt: '2024-01-01T00:00:00Z',
        completedAt: '2024-01-01T00:01:00Z',
      });
    });

    it('should validate required fields', async () => {
      const request = new NextRequest('http://localhost:3000/api/generate-video', {
        method: 'POST',
        body: JSON.stringify({
          imageUrl: 'https://example.com/image.jpg',
          // Missing prompt
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing required fields: imageUrl and prompt');
    });

    it('should validate prompt length', async () => {
      const request = new NextRequest('http://localhost:3000/api/generate-video', {
        method: 'POST',
        body: JSON.stringify({
          imageUrl: 'https://example.com/image.jpg',
          prompt: 'short',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Prompt must be at least 10 characters long');
    });

    it('should validate duration', async () => {
      const request = new NextRequest('http://localhost:3000/api/generate-video', {
        method: 'POST',
        body: JSON.stringify({
          imageUrl: 'https://example.com/image.jpg',
          prompt: 'A beautiful landscape with flowing water',
          duration: 15, // Invalid duration
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Duration must be 5 or 10 seconds');
    });

    it('should handle generation failure', async () => {
      mockRunwayService.generateVideoAndWait.mockResolvedValue({
        id: 'test-task-id',
        status: 'failed',
        error: 'Generation failed',
      });

      const request = new NextRequest('http://localhost:3000/api/generate-video', {
        method: 'POST',
        body: JSON.stringify({
          imageUrl: 'https://example.com/image.jpg',
          prompt: 'A beautiful landscape with flowing water',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Generation failed');
    });

    it('should handle service errors', async () => {
      mockRunwayService.generateVideoAndWait.mockRejectedValue(
        new Error('Service unavailable')
      );

      const request = new NextRequest('http://localhost:3000/api/generate-video', {
        method: 'POST',
        body: JSON.stringify({
          imageUrl: 'https://example.com/image.jpg',
          prompt: 'A beautiful landscape with flowing water',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Service unavailable');
    });
  });

  describe('GET', () => {
    it('should get generation status successfully', async () => {
      const mockStatus = {
        id: 'test-task-id',
        status: 'completed',
        videoUrl: 'https://example.com/video.mp4',
        progress: 100,
        createdAt: '2024-01-01T00:00:00Z',
        completedAt: '2024-01-01T00:01:00Z',
      };

      mockRunwayService.getGenerationStatus.mockResolvedValue(mockStatus);

      const request = new NextRequest(
        'http://localhost:3000/api/generate-video?id=test-task-id'
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockStatus);
    });

    it('should validate generation ID', async () => {
      const request = new NextRequest('http://localhost:3000/api/generate-video');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing generation ID');
    });

    it('should handle status check errors', async () => {
      mockRunwayService.getGenerationStatus.mockRejectedValue(
        new Error('Task not found')
      );

      const request = new NextRequest(
        'http://localhost:3000/api/generate-video?id=invalid-id'
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Task not found');
    });
  });
});
