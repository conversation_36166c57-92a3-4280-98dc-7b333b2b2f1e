// Mock the RunwayML SDK before importing our module
jest.mock('@runwayml/sdk', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    imageToVideo: {
      create: jest.fn(),
    },
    tasks: {
      retrieve: jest.fn(),
    },
  })),
  TaskFailedError: class TaskFailedError extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'TaskFailedError';
    }
  },
}));

import {
  createRunwayMLService,
  prepareImageForRunway,
  optimizePromptForVideo,
  createImageToVideoRequest,
  imageToDataUri,
  MockRunwayMLService,
  type ImageToVideoRequest
} from '@/lib/runwayml';

// Mock environment variables
const originalEnv = process.env;

beforeEach(() => {
  jest.resetModules();
  process.env = { ...originalEnv };
});

afterAll(() => {
  process.env = originalEnv;
});

describe('RunwayML Service', () => {
  describe('createRunwayMLService', () => {
    it('should return MockRunwayMLService when no API key is provided', () => {
      delete process.env.RUNWAY_API_KEY;
      const service = createRunwayMLService();
      expect(service).toBeInstanceOf(MockRunwayMLService);
    });

    it('should return RunwayMLService when API key is provided', () => {
      process.env.RUNWAY_API_KEY = 'test-api-key';
      const service = createRunwayMLService();
      expect(service).not.toBeInstanceOf(MockRunwayMLService);
    });
  });

  describe('prepareImageForRunway', () => {
    it('should return URL as-is if it starts with http', async () => {
      const url = 'https://example.com/image.jpg';
      const result = await prepareImageForRunway(url);
      expect(result).toBe(url);
    });

    it('should convert relative URL to absolute', async () => {
      process.env.NEXT_PUBLIC_BASE_URL = 'https://myapp.com';
      const result = await prepareImageForRunway('/images/test.jpg');
      expect(result).toBe('https://myapp.com/images/test.jpg');
    });

    it('should use localhost as default base URL', async () => {
      delete process.env.NEXT_PUBLIC_BASE_URL;
      const result = await prepareImageForRunway('/images/test.jpg');
      expect(result).toBe('http://localhost:3000/images/test.jpg');
    });
  });

  describe('optimizePromptForVideo', () => {
    it('should add motion context if not present', () => {
      const prompt = 'A beautiful landscape';
      const result = optimizePromptForVideo(prompt);
      expect(result).toContain('gentle, natural movement');
    });

    it('should not modify prompt if motion keywords are present', () => {
      const prompt = 'A beautiful landscape with flowing water';
      const result = optimizePromptForVideo(prompt);
      expect(result).toBe(prompt);
    });

    it('should detect various motion keywords', () => {
      const prompts = [
        'A scene with motion',
        'Dynamic action sequence',
        'Flowing animation',
        'Objects that moves gracefully'
      ];

      prompts.forEach(prompt => {
        const result = optimizePromptForVideo(prompt);
        expect(result).toBe(prompt);
      });
    });
  });

  describe('imageToDataUri', () => {
    it('should return data URI as-is if already formatted', () => {
      const dataUri = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ';
      const result = imageToDataUri(dataUri);
      expect(result).toBe(dataUri);
    });

    it('should format base64 string as data URI', () => {
      const base64 = '/9j/4AAQSkZJRgABAQAAAQ';
      const result = imageToDataUri(base64);
      expect(result).toBe('data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ');
    });

    it('should use custom MIME type', () => {
      const base64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB';
      const result = imageToDataUri(base64, 'image/png');
      expect(result).toBe('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB');
    });
  });

  describe('createImageToVideoRequest', () => {
    it('should create request with default values', () => {
      const request = createImageToVideoRequest(
        'https://example.com/image.jpg',
        'A beautiful scene'
      );

      expect(request).toEqual({
        model: 'gen4_turbo',
        promptImage: 'https://example.com/image.jpg',
        promptText: 'A beautiful scene. The scene comes alive with gentle, natural movement.',
        duration: 5,
        ratio: '1280:720',
      });
    });

    it('should use provided options', () => {
      const request = createImageToVideoRequest(
        'https://example.com/image.jpg',
        'A dynamic scene with motion',
        {
          duration: 10,
          ratio: '1920:1080',
          seed: 12345,
        }
      );

      expect(request).toEqual({
        model: 'gen4_turbo',
        promptImage: 'https://example.com/image.jpg',
        promptText: 'A dynamic scene with motion',
        duration: 10,
        ratio: '1920:1080',
        seed: 12345,
      });
    });
  });

  describe('MockRunwayMLService', () => {
    let mockService: MockRunwayMLService;

    beforeEach(() => {
      mockService = new MockRunwayMLService();
    });

    it('should generate video with mock response', async () => {
      const request: ImageToVideoRequest = {
        model: 'gen4_turbo',
        promptImage: 'https://example.com/image.jpg',
        promptText: 'Test prompt',
      };

      const result = await mockService.generateVideo(request);

      expect(result).toMatchObject({
        id: expect.stringMatching(/^mock_\d+$/),
        status: 'pending',
        createdAt: expect.any(String),
      });
    });

    it('should return completed status for status check', async () => {
      const result = await mockService.getGenerationStatus('test-id');

      expect(result).toMatchObject({
        id: 'test-id',
        status: 'completed',
        videoUrl: expect.stringContaining('mock_generated_'),
        createdAt: expect.any(String),
        completedAt: expect.any(String),
      });
    });

    it('should complete video generation with wait', async () => {
      const request: ImageToVideoRequest = {
        model: 'gen4_turbo',
        promptImage: 'https://example.com/image.jpg',
        promptText: 'Test prompt',
      };

      const result = await mockService.generateVideoAndWait(request);

      expect(result).toMatchObject({
        id: expect.stringMatching(/^mock_\d+$/),
        status: 'completed',
        videoUrl: '/videos/mock_generated_video.mp4',
        createdAt: expect.any(String),
        completedAt: expect.any(String),
      });
    });
  });
});
