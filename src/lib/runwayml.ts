import RunwayML, { TaskFailedError } from '@runwayml/sdk';
import fs from 'fs';
import path from 'path';
import { pipeline } from 'stream/promises';

interface RunwayMLConfig {
  apiKey: string;
  baseUrl?: string;
}

// Request interface for image-to-video generation
interface ImageToVideoRequest {
  model: 'gen4_turbo';
  promptImage: string; // URL or base64 data URI
  promptText: string;
  duration?: 5 | 10; // Duration in seconds for gen4_turbo
  ratio?: '1280:720' | '720:1280' | '1104:832' | '832:1104' | '960:960' | '1584:672' | '1280:768' | '768:1280';
  seed?: number;
}



// Legacy interface for backward compatibility
interface GenerationResponse {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  videoUrl?: string; // Original Runway video URL
  localVideoPath?: string; // Local saved video path
  error?: string;
  createdAt: string;
  completedAt?: string;
}

class RunwayMLService {
  private client: RunwayML;

  constructor(config: RunwayMLConfig) {
    this.client = new RunwayML({
      apiKey: config.apiKey,
      baseURL: config.baseUrl,
    });
  }

  /**
   * Generate a video from an image using RunwayML Gen-4 Turbo
   */
  async generateVideo(request: ImageToVideoRequest): Promise<GenerationResponse> {
    try {
      const task = await this.client.imageToVideo.create({
        model: request.model,
        promptImage: request.promptImage,
        promptText: request.promptText,
        duration: request.duration || 5,
        ratio: request.ratio || '1280:720',
        seed: request.seed,
      });

      return this.convertTaskToResponse(task as unknown as Record<string, unknown>);

    } catch (error) {
      console.error('RunwayML generation error:', error);
      if (error instanceof TaskFailedError) {
        throw new Error(`Video generation failed: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Get the status of a generation task
   */
  async getGenerationStatus(taskId: string): Promise<GenerationResponse> {
    try {
      const task = await this.client.tasks.retrieve(taskId);
      return this.convertTaskToResponse(task as unknown as Record<string, unknown>);

    } catch (error) {
      console.error('RunwayML status check error:', error);
      throw error;
    }
  }

  /**
   * Poll for task completion with automatic retries
   */
  async pollForCompletion(
    taskId: string,
    maxAttempts: number = 60, // Increased for video generation
    intervalMs: number = 5000 // 5 seconds between polls
  ): Promise<GenerationResponse> {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const status = await this.getGenerationStatus(taskId);

      if (status.status === 'completed' || status.status === 'failed') {
        return status;
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, intervalMs));
    }

    throw new Error('Generation timed out after maximum attempts');
  }

  /**
   * Generate video and wait for completion
   */
  async generateVideoAndWait(request: ImageToVideoRequest): Promise<GenerationResponse> {
    try {
      const task = await this.client.imageToVideo
        .create({
          model: request.model,
          promptImage: request.promptImage,
          promptText: request.promptText,
          duration: request.duration || 5,
          ratio: request.ratio || '1280:720',
          seed: request.seed,
        })
        .waitForTaskOutput();

      const response = this.convertTaskToResponse(task as unknown as Record<string, unknown>);

      // Download and save video locally if generation was successful
      if (response.status === 'completed' && response.videoUrl) {
        try {
          const filename = `runway_${response.id}`;
          const localPath = await this.downloadVideo(response.videoUrl, filename);
          response.localVideoPath = localPath;
          console.log(`Video saved locally: ${localPath}`);
        } catch (downloadError) {
          console.error('Failed to download video locally:', downloadError);
          // Don't fail the entire request if download fails
        }
      }

      return response;

    } catch (error) {
      console.error('RunwayML generation error:', error);
      if (error instanceof TaskFailedError) {
        throw new Error(`Video generation failed: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Download video from URL and save locally
   */
  async downloadVideo(videoUrl: string, filename: string): Promise<string> {
    try {
      // Create videos directory if it doesn't exist
      const videosDir = path.join(process.cwd(), 'public', 'generated-videos');
      if (!fs.existsSync(videosDir)) {
        fs.mkdirSync(videosDir, { recursive: true });
      }

      // Generate unique filename with timestamp
      const timestamp = Date.now();
      // Always use .mp4 extension for consistency
      const safeFilename = `${filename}_${timestamp}.mp4`;
      const localPath = path.join(videosDir, safeFilename);

      // Download the video
      const response = await fetch(videoUrl);
      if (!response.ok) {
        throw new Error(`Failed to download video: ${response.statusText}`);
      }

      // Save to local file
      const fileStream = fs.createWriteStream(localPath);
      if (response.body) {
        await pipeline(response.body as unknown as NodeJS.ReadableStream, fileStream);
      } else {
        throw new Error('Response body is null');
      }

      // Return the public URL path
      return `/generated-videos/${safeFilename}`;
    } catch (error) {
      console.error('Video download error:', error);
      throw new Error(`Failed to download video: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert RunwayML task to our response format
   */
  private convertTaskToResponse(task: Record<string, unknown>): GenerationResponse {
    const statusMap: Record<string, GenerationResponse['status']> = {
      'PENDING': 'pending',
      'RUNNING': 'processing',
      'SUCCEEDED': 'completed',
      'FAILED': 'failed',
      'CANCELLED': 'failed',
    };

    const taskId = task.id as string;
    const taskStatus = task.status as string;
    const taskProgress = task.progress as number | undefined;
    const taskOutput = task.output as string[] | undefined;
    const taskFailure = task.failure as string | undefined;
    const taskFailureCode = task.failureCode as string | undefined;
    const taskCreatedAt = task.createdAt as string;
    const taskCompletedAt = task.completedAt as string | undefined;

    return {
      id: taskId,
      status: statusMap[taskStatus] || 'pending',
      progress: taskProgress,
      videoUrl: taskOutput?.[0], // First video URL from output array
      error: taskFailure || taskFailureCode,
      createdAt: taskCreatedAt,
      completedAt: taskCompletedAt,
    };
  }
}

// Factory function to create RunwayML service instance
export function createRunwayMLService(): RunwayMLService | MockRunwayMLService {
  const apiKey = process.env.RUNWAY_API_KEY;
  const baseUrl = process.env.RUNWAY_BASE_URL || 'https://api.dev.runwayml.com';

  // Return mock service if no API key is provided (for development)
  if (!apiKey) {
    console.warn('RUNWAY_API_KEY not found, using mock service for development');
    return new MockRunwayMLService();
  }

  return new RunwayMLService({
    apiKey,
    baseUrl,
  });
}

// Helper function to prepare image for RunwayML
export async function prepareImageForRunway(imageUrl: string): Promise<string> {
  // If it's already a HTTPS URL, return as-is
  if (imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // If it's HTTP, we need to convert to data URI since RunwayML only accepts HTTPS
  if (imageUrl.startsWith('http://')) {
    return await convertUrlToDataUri(imageUrl);
  }

  // If it's a local file path, convert to data URI
  if (imageUrl.startsWith('/')) {
    const fullUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}${imageUrl}`;
    return await convertUrlToDataUri(fullUrl);
  }

  // If it's already a data URI, return as-is
  if (imageUrl.startsWith('data:')) {
    return imageUrl;
  }

  return imageUrl;
}

// Helper function to convert URL to data URI
async function convertUrlToDataUri(url: string): Promise<string> {
  try {
    // In Node.js environment (API routes)
    if (typeof window === 'undefined') {
      const fs = await import('fs');
      const path = await import('path');

      // Extract the file path from the URL
      const urlObj = new URL(url);
      const filePath = path.join(process.cwd(), 'public', urlObj.pathname);

      // Check if file exists
      if (fs.existsSync(filePath)) {
        const fileBuffer = fs.readFileSync(filePath);
        const base64 = fileBuffer.toString('base64');

        // Determine MIME type based on file extension
        const ext = path.extname(filePath).toLowerCase();
        const mimeType = getMimeType(ext);

        return `data:${mimeType};base64,${base64}`;
      } else {
        throw new Error(`File not found: ${filePath}`);
      }
    } else {
      // In browser environment
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.statusText}`);
      }

      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    }
  } catch (error) {
    console.error('Failed to convert URL to data URI:', error);
    throw new Error(`Failed to prepare image for RunwayML: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Helper function to get MIME type from file extension
function getMimeType(extension: string): string {
  const mimeTypes: Record<string, string> = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.svg': 'image/svg+xml',
  };

  return mimeTypes[extension] || 'image/jpeg';
}

// Helper function to optimize prompt for video generation
export function optimizePromptForVideo(prompt: string): string {
  // Add video-specific context if not present
  const videoKeywords = ['moves', 'motion', 'action', 'animation', 'flowing', 'dynamic'];
  const hasVideoContext = videoKeywords.some(keyword =>
    prompt.toLowerCase().includes(keyword)
  );

  if (!hasVideoContext) {
    // Add subtle motion context
    return `${prompt}. The scene comes alive with gentle, natural movement.`;
  }

  return prompt;
}

// Helper function to convert base64 image to data URI
export function imageToDataUri(base64: string, mimeType: string = 'image/jpeg'): string {
  if (base64.startsWith('data:')) {
    return base64; // Already a data URI
  }
  return `data:${mimeType};base64,${base64}`;
}

// Helper function to create image-to-video request
export function createImageToVideoRequest(
  imageUrl: string,
  prompt: string,
  options: Partial<ImageToVideoRequest> = {}
): ImageToVideoRequest {
  return {
    model: 'gen4_turbo',
    promptImage: imageUrl,
    promptText: optimizePromptForVideo(prompt),
    duration: options.duration || 5,
    ratio: options.ratio || '1280:720',
    seed: options.seed,
    ...options,
  };
}

// Mock service for development
export class MockRunwayMLService {
  async generateVideo(_request: ImageToVideoRequest): Promise<GenerationResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      id: `mock_${Date.now()}`,
      status: 'pending',
      createdAt: new Date().toISOString(),
    };
  }

  async getGenerationStatus(generationId: string): Promise<GenerationResponse> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      id: generationId,
      status: 'completed',
      videoUrl: `/videos/mock_generated_${generationId}.mp4`,
      createdAt: new Date(Date.now() - 10000).toISOString(),
      completedAt: new Date().toISOString(),
    };
  }

  async pollForCompletion(generationId: string): Promise<GenerationResponse> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    return this.getGenerationStatus(generationId);
  }

  async generateVideoAndWait(_request: ImageToVideoRequest): Promise<GenerationResponse> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 3000));

    const mockId = `mock_${Date.now()}`;
    const mockVideoPath = `/generated-videos/test_video.mp4`; // Use the test video we created

    return {
      id: mockId,
      status: 'completed',
      videoUrl: mockVideoPath, // Use local path for mock
      localVideoPath: mockVideoPath,
      createdAt: new Date(Date.now() - 3000).toISOString(),
      completedAt: new Date().toISOString(),
    };
  }
}

export { RunwayMLService, type ImageToVideoRequest, type GenerationResponse };
