'use client';

import { useAppStore } from '@/stores/appStore';
import Timeline from '@/components/Timeline';
import PhotoStage from '@/components/PhotoStage';
import ProgressIndicator from '@/components/ProgressIndicator';
import { motion } from 'framer-motion';

export default function Home() {
  const { photos, currentPhotoIndex, setCurrentPhoto } = useAppStore();
  const currentPhoto = photos[currentPhotoIndex];

  const handlePhotoSelect = (index: number) => {
    setCurrentPhoto(index);
  };

  const handlePromptSubmit = async (prompt: string) => {
    // TODO: Implement video generation logic
    console.log('Prompt submitted:', prompt);
  };

  return (
    <div className="min-h-screen bg-vintage-off-white relative">
      {/* Film grain overlay */}
      <div className="film-grain fixed inset-0 pointer-events-none z-10" />

      {/* Progress Indicator */}
      <ProgressIndicator />

      {/* Main content container */}
      <div className="relative z-20 min-h-screen flex flex-col">
        {/* Header */}
        <motion.header
          className="text-center py-6 md:py-8 px-4 flex-shrink-0"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="font-playfair text-3xl sm:text-4xl md:text-5xl text-vintage-brown mb-3">
            Moments Forward
          </h1>
          <div className="text-container mx-auto px-4">
            <p className="font-inter text-sm md:text-base text-vintage-brown/80 leading-relaxed description-text text-center">
              A journey through our shared memories, where each photo unlocks the next chapter of our story
            </p>
          </div>
        </motion.header>

        {/* Main content area - Centered and properly spaced */}
        <div className="flex-1 flex items-center justify-center px-4 py-4">
          <div className="w-full max-w-6xl mx-auto min-w-0">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <PhotoStage
                photo={currentPhoto}
                isActive={true}
                onPromptSubmit={handlePromptSubmit}
              />
            </motion.div>
          </div>
        </div>

        {/* Timeline - Fixed at bottom with proper spacing */}
        <motion.div
          className="flex-shrink-0 py-4 md:py-6 px-4 bg-vintage-off-white/80 backdrop-blur-sm border-t border-vintage-gold/10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="max-w-6xl mx-auto">
            <Timeline
              photos={photos}
              currentIndex={currentPhotoIndex}
              onPhotoSelect={handlePhotoSelect}
            />
          </div>
        </motion.div>
      </div>
    </div>
  );
}
