@import "tailwindcss";

:root {
  /* Vintage Color Palette */
  --vintage-sepia: #F4E4BC;
  --vintage-gold: #D4AF37;
  --vintage-cream: #FFF8DC;
  --vintage-brown: #3C2415;
  --vintage-off-white: #FEFDF9;

  /* Spacing System (8px base) */
  --spacing-xs: 8px;
  --spacing-sm: 16px;
  --spacing-md: 24px;
  --spacing-lg: 32px;
  --spacing-xl: 48px;
  --spacing-2xl: 64px;
}

@theme inline {
  /* Custom Colors */
  --color-vintage-sepia: var(--vintage-sepia);
  --color-vintage-gold: var(--vintage-gold);
  --color-vintage-cream: var(--vintage-cream);
  --color-vintage-brown: var(--vintage-brown);
  --color-vintage-off-white: var(--vintage-off-white);

  /* Typography */
  --font-playfair: var(--font-playfair);
  --font-inter: var(--font-inter);
  --font-kalam: var(--font-kalam);
  --font-poppins: var(--font-poppins);

  /* Spacing */
  --spacing-xs: var(--spacing-xs);
  --spacing-sm: var(--spacing-sm);
  --spacing-md: var(--spacing-md);
  --spacing-lg: var(--spacing-lg);
  --spacing-xl: var(--spacing-xl);
  --spacing-2xl: var(--spacing-2xl);
}

body {
  background: var(--vintage-off-white);
  color: var(--vintage-brown);
  font-family: var(--font-inter), system-ui, sans-serif;
}

/* Film grain texture overlay */
.film-grain::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.03;
  background-image:
    radial-gradient(circle at 20% 50%, transparent 20%, rgba(255,255,255,0.3) 21%, rgba(255,255,255,0.3) 34%, transparent 35%, transparent),
    linear-gradient(0deg, transparent 24%, rgba(255,255,255,0.05) 25%, rgba(255,255,255,0.05) 26%, transparent 27%, transparent 74%, rgba(255,255,255,0.05) 75%, rgba(255,255,255,0.05) 76%, transparent 77%, transparent);
  background-size: 50px 50px;
  pointer-events: none;
}

/* Vintage photo frame effect */
.vintage-frame {
  border: 8px solid white;
  box-shadow:
    0 0 0 1px rgba(0,0,0,0.1),
    0 4px 8px rgba(0,0,0,0.15),
    inset 0 0 0 1px rgba(0,0,0,0.05);
}

/* Golden glow effect */
.golden-glow {
  box-shadow:
    0 0 20px rgba(212, 175, 55, 0.3),
    0 0 40px rgba(212, 175, 55, 0.2),
    0 0 60px rgba(212, 175, 55, 0.1);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Container utilities */
.container-centered {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Responsive spacing utilities */
.section-spacing {
  padding: 2rem 0;
}

@media (min-width: 768px) {
  .section-spacing {
    padding: 4rem 0;
  }
}

/* Improved focus states */
*:focus-visible {
  outline: 2px solid var(--vintage-gold);
  outline-offset: 2px;
}

/* Better button transitions */
button {
  transition: all 0.2s ease-in-out;
}

/* Ensure proper text rendering */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Layout improvements */
.main-content-area {
  min-height: calc(100vh - 200px);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Better spacing for components */
.photo-stage-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  width: 100%;
  max-width: 48rem;
  margin: 0 auto;
}

/* Timeline improvements */
.timeline-container {
  background: rgba(254, 253, 249, 0.8);
  backdrop-filter: blur(8px);
  border-top: 1px solid rgba(212, 175, 55, 0.1);
}

/* Text wrapping fixes */
.story-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  white-space: normal;
  min-width: 0;
  width: 100%;
  display: block;
}

.description-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  min-width: 0;
  width: 100%;
  display: block;
}

/* Prevent excessive narrow containers */
.text-container {
  min-width: 280px;
  max-width: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .photo-stage-container {
    gap: 1rem;
    padding: 0 1rem;
  }

  .main-content-area {
    min-height: calc(100vh - 160px);
    padding: 1rem 0;
  }

  .text-container {
    min-width: 240px;
  }
}
