import { useState, useCallback } from 'react';
import { useAppStore } from '@/stores/appStore';
import { generateId } from '@/lib/utils';
import { VideoGeneration } from '@/types';

export const useVideoGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const { addVideoGeneration, updateVideoGeneration, unlockNextPhoto } = useAppStore();

  const generateVideo = useCallback(async (
    photoId: string,
    prompt: string,
    options: {
      duration?: 5 | 10;
      ratio?: '1280:720' | '720:1280' | '1104:832' | '832:1104' | '960:960' | '1584:672' | '1280:768' | '768:1280';
      seed?: number;
    } = {}
  ): Promise<string | null> => {
    setIsGenerating(true);

    // Create video generation record
    const generation: VideoGeneration = {
      id: generateId(),
      photoId,
      prompt,
      status: 'pending',
      createdAt: new Date(),
    };

    addVideoGeneration(generation);

    try {
      // Update status to generating
      updateVideoGeneration(generation.id, { status: 'generating' });

      // Call our API endpoint for video generation
      const response = await fetch('/api/generate-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: `/photos/photo_${photoId}.jpg`, // Use the photo as base image
          prompt,
          duration: options.duration || 5,
          ratio: options.ratio || '1280:720',
          seed: options.seed,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Video generation failed');
      }

      const videoUrl = data.videoUrl;

      updateVideoGeneration(generation.id, {
        status: 'completed',
        videoUrl: videoUrl, // This will be the local path if available
        originalVideoUrl: data.originalVideoUrl,
        localVideoPath: data.localVideoPath,
        runwayTaskId: data.generationId,
      });

      // Unlock next photo after successful generation
      unlockNextPhoto();

      setIsGenerating(false);
      return videoUrl;

    } catch (error) {
      console.error('Video generation failed:', error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      updateVideoGeneration(generation.id, {
        status: 'failed',
        error: errorMessage,
      });

      setIsGenerating(false);
      return null;
    }
  }, [addVideoGeneration, updateVideoGeneration, unlockNextPhoto]);

  return {
    generateVideo,
    isGenerating,
  };
};

// Hook for RunwayML API integration with status polling
export const useRunwayML = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState<number | undefined>();

  const generateVideoWithRunway = useCallback(async (
    imageUrl: string,
    prompt: string,
    options: {
      duration?: 5 | 10;
      ratio?: '1280:720' | '720:1280' | '1104:832' | '832:1104' | '960:960' | '1584:672' | '1280:768' | '768:1280';
      seed?: number;
      pollForCompletion?: boolean;
    } = {}
  ): Promise<{ videoUrl: string | null; taskId: string | null }> => {
    setIsGenerating(true);
    setProgress(undefined);

    try {
      // Start video generation
      const response = await fetch('/api/generate-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl,
          prompt,
          duration: options.duration || 5,
          ratio: options.ratio || '1280:720',
          seed: options.seed,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Video generation failed');
      }

      const taskId = data.generationId;

      // If polling is disabled or video is already completed, return immediately
      if (!options.pollForCompletion || data.status === 'completed') {
        setIsGenerating(false);
        return { videoUrl: data.videoUrl, taskId };
      }

      // Poll for completion
      let attempts = 0;
      const maxAttempts = 60; // 5 minutes with 5-second intervals

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds

        const statusResponse = await fetch(`/api/generate-video?id=${taskId}`);

        if (!statusResponse.ok) {
          console.warn('Failed to check status, continuing to poll...');
          attempts++;
          continue;
        }

        const statusData = await statusResponse.json();
        setProgress(statusData.progress);

        if (statusData.status === 'completed') {
          setIsGenerating(false);
          return { videoUrl: statusData.videoUrl, taskId };
        }

        if (statusData.status === 'failed') {
          throw new Error(statusData.error || 'Video generation failed');
        }

        attempts++;
      }

      throw new Error('Video generation timed out');

    } catch (error) {
      console.error('RunwayML API error:', error);
      setIsGenerating(false);
      setProgress(undefined);
      return { videoUrl: null, taskId: null };
    }
  }, []);

  const checkStatus = useCallback(async (taskId: string) => {
    try {
      const response = await fetch(`/api/generate-video?id=${taskId}`);

      if (!response.ok) {
        throw new Error('Failed to check status');
      }

      return await response.json();
    } catch (error) {
      console.error('Status check error:', error);
      return null;
    }
  }, []);

  return {
    generateVideoWithRunway,
    checkStatus,
    isGenerating,
    progress,
  };
};
