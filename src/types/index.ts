export interface Photo {
  id: string;
  src: string;
  alt: string;
  date?: string;
  location?: string;
  isUnlocked: boolean;
  contextStory: string;
  brotherSignature: string;
}

export interface VideoGeneration {
  id: string;
  photoId: string;
  prompt: string;
  videoUrl?: string; // Local video path (preferred) or original URL
  originalVideoUrl?: string; // Original Runway video URL
  localVideoPath?: string; // Local saved video path
  status: 'pending' | 'generating' | 'completed' | 'failed';
  createdAt: Date;
  runwayTaskId?: string; // RunwayML task ID for status tracking
  error?: string; // Error message if generation failed
  progress?: number; // Generation progress (0-100)
}

export interface AppState {
  photos: Photo[];
  currentPhotoIndex: number;
  videoGenerations: VideoGeneration[];
  isLoading: boolean;
  error?: string;
}

export interface TimelineProps {
  photos: Photo[];
  currentIndex: number;
  onPhotoSelect: (index: number) => void;
}

export interface PhotoStageProps {
  photo: Photo;
  isActive: boolean;
  onPromptSubmit: (prompt: string) => void;
}

export interface StoryContextProps {
  story: string;
  signature: string;
  isVisible: boolean;
}

export interface InputComponentProps {
  onSubmit: (prompt: string) => void;
  isLoading: boolean;
  placeholder?: string;
  maxLength?: number;
  minLength?: number;
}

export interface VideoPlayerProps {
  videoUrl: string;
  isVisible: boolean;
  onComplete: () => void;
}

export interface LoadingState {
  type: 'photo-unlock' | 'video-generation' | 'page-transition';
  message: string;
}
