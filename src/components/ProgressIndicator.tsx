'use client';

import { motion } from 'framer-motion';
import { useAppStore } from '@/stores/appStore';

const ProgressIndicator: React.FC = () => {
  const { photos, getTotalProgress, getCompletedVideos } = useAppStore();
  const progress = getTotalProgress();
  const completedVideos = getCompletedVideos();
  const unlockedPhotos = photos.filter(photo => photo.isUnlocked).length;

  return (
    <div className="fixed top-4 right-4 z-30">
      <motion.div
        className="bg-vintage-cream/90 backdrop-blur-sm rounded-2xl p-3 md:p-4 border border-vintage-gold/20 shadow-lg"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center space-x-3 md:space-x-4">
          {/* Circular progress */}
          <div className="relative w-10 h-10 md:w-12 md:h-12">
            <svg className="w-10 h-10 md:w-12 md:h-12 transform -rotate-90" viewBox="0 0 36 36">
              {/* Background circle */}
              <path
                className="text-vintage-brown/20"
                stroke="currentColor"
                strokeWidth="3"
                fill="transparent"
                d="M18 2.0845
                  a 15.9155 15.9155 0 0 1 0 31.831
                  a 15.9155 15.9155 0 0 1 0 -31.831"
              />
              {/* Progress circle */}
              <motion.path
                className="text-vintage-gold"
                stroke="currentColor"
                strokeWidth="3"
                fill="transparent"
                strokeLinecap="round"
                d="M18 2.0845
                  a 15.9155 15.9155 0 0 1 0 31.831
                  a 15.9155 15.9155 0 0 1 0 -31.831"
                initial={{ strokeDasharray: "0 100" }}
                animate={{ strokeDasharray: `${progress} 100` }}
                transition={{ duration: 1, ease: "easeInOut" }}
              />
            </svg>
            
            {/* Progress text */}
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xs md:text-sm font-poppins font-semibold text-vintage-brown">
                {progress}%
              </span>
            </div>
          </div>

          {/* Progress details */}
          <div className="text-xs md:text-sm hidden sm:block">
            <div className="font-poppins font-medium text-vintage-brown">
              {unlockedPhotos} of {photos.length}
            </div>
            <div className="font-inter text-vintage-brown/60">
              memories unlocked
            </div>
            {completedVideos.length > 0 && (
              <div className="font-inter text-vintage-gold text-xs mt-1">
                {completedVideos.length} videos created
              </div>
            )}
          </div>
        </div>

        {/* Mini timeline */}
        <div className="mt-3 flex space-x-1">
          {photos.map((photo, index) => (
            <motion.div
              key={photo.id}
              className={`w-2 h-2 rounded-full ${
                photo.isUnlocked 
                  ? 'bg-vintage-gold' 
                  : 'bg-vintage-brown/20'
              }`}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default ProgressIndicator;
