'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Camera, Send } from 'lucide-react';
import { InputComponentProps } from '@/types';

const InputComponent: React.FC<InputComponentProps> = ({
  onSubmit,
  isLoading,
  placeholder = "Imagine what happened in the next 10 seconds...",
  maxLength = 300,
  minLength = 20
}) => {
  const [prompt, setPrompt] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (prompt.trim().length >= minLength && !isLoading) {
      onSubmit(prompt.trim());
      setPrompt('');
    }
  };

  const isValid = prompt.trim().length >= minLength;
  const remainingChars = maxLength - prompt.length;

  return (
    <motion.div
      className="w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Input field */}
        <div className="relative">
          <motion.div
            className={`
              relative bg-vintage-cream/50 backdrop-blur-sm rounded-2xl border-2 transition-all duration-300
              ${isFocused ? 'border-vintage-gold shadow-lg' : 'border-vintage-gold/30'}
            `}
            whileFocus={{ scale: 1.02 }}
          >
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder={placeholder}
              maxLength={maxLength}
              rows={4}
              className="w-full p-4 md:p-6 bg-transparent resize-none outline-none font-inter text-vintage-brown placeholder-vintage-brown/50 text-base md:text-lg leading-relaxed"
              disabled={isLoading}
            />
            
            {/* Film perforations character counter */}
            <div className="absolute bottom-3 right-3 md:bottom-4 md:right-4 flex items-center space-x-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <div
                  key={i}
                  className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                    i < Math.floor((prompt.length / maxLength) * 5)
                      ? 'bg-vintage-gold'
                      : 'bg-vintage-brown/20'
                  }`}
                />
              ))}
              <span className="ml-2 font-poppins text-sm text-vintage-brown/60">
                {remainingChars}
              </span>
            </div>
          </motion.div>
        </div>

        {/* Submit button */}
        <div className="flex justify-center">
          <motion.button
            type="submit"
            disabled={!isValid || isLoading}
            className={`
              flex items-center space-x-2 md:space-x-3 px-6 py-3 md:px-8 md:py-4 rounded-full font-poppins font-medium text-base md:text-lg transition-all duration-300 shadow-lg
              ${isValid && !isLoading
                ? 'bg-vintage-gold hover:bg-vintage-gold/90 text-vintage-brown hover:shadow-xl transform hover:scale-105'
                : 'bg-vintage-brown/20 text-vintage-brown/40 cursor-not-allowed'
              }
            `}
            whileHover={isValid && !isLoading ? { scale: 1.05 } : {}}
            whileTap={isValid && !isLoading ? { scale: 0.95 } : {}}
          >
            {isLoading ? (
              <>
                <motion.div
                  className="w-6 h-6 border-2 border-vintage-brown/30 border-t-vintage-brown rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                <span>Creating magic...</span>
              </>
            ) : (
              <>
                <Camera className="w-6 h-6" />
                <span>Generate Video</span>
                <Send className="w-5 h-5" />
              </>
            )}
          </motion.button>
        </div>

        {/* Validation message */}
        {prompt.length > 0 && prompt.trim().length < minLength && (
          <motion.p
            className="text-center font-inter text-sm text-vintage-brown/60"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            Please write at least {minLength} characters to create your video
          </motion.p>
        )}
      </form>
    </motion.div>
  );
};

export default InputComponent;
