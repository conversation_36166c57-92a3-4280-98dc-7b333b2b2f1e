'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';

interface UnlockAnimationProps {
  isVisible: boolean;
  onComplete: () => void;
  photoIndex: number;
}

const UnlockAnimation: React.FC<UnlockAnimationProps> = ({ 
  isVisible, 
  onComplete, 
  photoIndex 
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 bg-vintage-off-white/80 backdrop-blur-sm flex items-center justify-center z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onAnimationComplete={() => {
            setTimeout(onComplete, 2000); // Show for 2 seconds
          }}
        >
          <motion.div
            className="text-center space-y-8"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            {/* Sparkle burst animation */}
            <div className="relative w-32 h-32 mx-auto">
              {/* Central heart */}
              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                initial={{ scale: 0 }}
                animate={{ scale: [0, 1.2, 1] }}
                transition={{ duration: 0.8, times: [0, 0.6, 1] }}
              >
                <Heart className="w-16 h-16 text-vintage-gold fill-vintage-gold" />
              </motion.div>

              {/* Sparkles */}
              {Array.from({ length: 12 }).map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-3 h-3"
                  style={{
                    left: '50%',
                    top: '50%',
                    transformOrigin: '0 0',
                  }}
                  initial={{ 
                    scale: 0, 
                    rotate: i * 30,
                    x: 0,
                    y: 0,
                  }}
                  animate={{ 
                    scale: [0, 1, 0],
                    x: Math.cos((i * 30) * Math.PI / 180) * 60,
                    y: Math.sin((i * 30) * Math.PI / 180) * 60,
                  }}
                  transition={{ 
                    duration: 1.5,
                    delay: 0.3 + (i * 0.05),
                    ease: "easeOut"
                  }}
                >
                  <Sparkles className="w-3 h-3 text-vintage-gold" />
                </motion.div>
              ))}

              {/* Ripple effect */}
              {Array.from({ length: 3 }).map((_, i) => (
                <motion.div
                  key={`ripple-${i}`}
                  className="absolute inset-0 border-2 border-vintage-gold/30 rounded-full"
                  initial={{ scale: 0, opacity: 1 }}
                  animate={{ 
                    scale: [0, 2, 3],
                    opacity: [1, 0.5, 0]
                  }}
                  transition={{ 
                    duration: 2,
                    delay: i * 0.3,
                    ease: "easeOut"
                  }}
                />
              ))}
            </div>

            {/* Success message */}
            <motion.div
              className="space-y-4"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <h2 className="font-playfair text-3xl text-vintage-brown">
                Memory Unlocked!
              </h2>
              <p className="font-kalam text-lg text-vintage-brown/80">
                Photo {photoIndex + 1} is now available
              </p>
              <motion.p
                className="font-inter text-vintage-brown/60"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 1 }}
              >
                Your story brought this moment to life ✨
              </motion.p>
            </motion.div>

            {/* Progress celebration */}
            <motion.div
              className="flex justify-center space-x-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1.2 }}
            >
              {Array.from({ length: 5 }).map((_, i) => (
                <motion.div
                  key={i}
                  className={`w-3 h-3 rounded-full ${
                    i <= photoIndex ? 'bg-vintage-gold' : 'bg-vintage-brown/20'
                  }`}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ 
                    duration: 0.3, 
                    delay: 1.2 + (i * 0.1) 
                  }}
                />
              ))}
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default UnlockAnimation;
