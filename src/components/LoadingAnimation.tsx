'use client';

import { motion } from 'framer-motion';
import { LoadingState } from '@/types';

interface LoadingAnimationProps {
  loadingState: LoadingState;
  isVisible: boolean;
}

const LoadingAnimation: React.FC<LoadingAnimationProps> = ({ loadingState, isVisible }) => {
  const getLoadingContent = () => {
    switch (loadingState.type) {
      case 'photo-unlock':
        return (
          <div className="flex flex-col items-center space-y-6">
            {/* Sparkle animation */}
            <div className="relative w-24 h-24">
              {Array.from({ length: 8 }).map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-vintage-gold rounded-full"
                  style={{
                    left: '50%',
                    top: '50%',
                    transformOrigin: '0 40px',
                  }}
                  animate={{
                    rotate: [0, 360],
                    scale: [0, 1, 0],
                    opacity: [0, 1, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.25,
                    ease: "easeInOut",
                  }}
                />
              ))}
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  className="w-8 h-8 bg-vintage-gold rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
              </div>
            </div>
            <p className="font-kalam text-vintage-brown text-lg text-center">
              {loadingState.message}
            </p>
          </div>
        );

      case 'video-generation':
        return (
          <div className="flex flex-col items-center space-y-6">
            {/* Film reel animation */}
            <div className="relative">
              <motion.div
                className="w-20 h-20 border-4 border-vintage-gold/30 rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <div className="absolute inset-2 border-2 border-vintage-gold/50 rounded-full">
                  <div className="absolute inset-2 border border-vintage-gold rounded-full">
                    {/* Film perforations */}
                    {Array.from({ length: 12 }).map((_, i) => (
                      <div
                        key={i}
                        className="absolute w-1 h-2 bg-vintage-gold/60 rounded-sm"
                        style={{
                          left: '50%',
                          top: '50%',
                          transformOrigin: '0 20px',
                          transform: `translate(-50%, -50%) rotate(${i * 30}deg)`,
                        }}
                      />
                    ))}
                  </div>
                </div>
              </motion.div>
              
              {/* Progress dots */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-1">
                {Array.from({ length: 3 }).map((_, i) => (
                  <motion.div
                    key={i}
                    className="w-2 h-2 bg-vintage-gold rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.3, 1, 0.3],
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      delay: i * 0.2,
                      ease: "easeInOut",
                    }}
                  />
                ))}
              </div>
            </div>
            
            <div className="text-center space-y-2">
              <p className="font-kalam text-vintage-brown text-lg">
                {loadingState.message}
              </p>
              <p className="font-inter text-vintage-brown/60 text-sm">
                This may take a few moments...
              </p>
            </div>
          </div>
        );

      case 'page-transition':
        return (
          <div className="flex flex-col items-center space-y-4">
            {/* Gentle fade animation */}
            <motion.div
              className="w-16 h-16 bg-vintage-gold/20 rounded-full"
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <p className="font-inter text-vintage-brown/80">
              {loadingState.message}
            </p>
          </div>
        );

      default:
        return (
          <div className="flex items-center space-x-3">
            <motion.div
              className="w-6 h-6 border-2 border-vintage-gold/30 border-t-vintage-gold rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <p className="font-inter text-vintage-brown">
              {loadingState.message}
            </p>
          </div>
        );
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      className="fixed inset-0 bg-vintage-off-white/80 backdrop-blur-sm flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        className="bg-vintage-cream/90 backdrop-blur-sm rounded-2xl p-8 border border-vintage-gold/20 shadow-xl"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        {getLoadingContent()}
      </motion.div>
    </motion.div>
  );
};

export default LoadingAnimation;
