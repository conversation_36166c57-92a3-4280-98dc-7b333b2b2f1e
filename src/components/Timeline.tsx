'use client';

import { motion } from 'framer-motion';
import { Lock, CheckCircle } from 'lucide-react';
import Image from 'next/image';
import { TimelineProps } from '@/types';
import { staggerContainer, staggerItem } from '@/lib/utils';

const Timeline: React.FC<TimelineProps> = ({ photos, currentIndex, onPhotoSelect }) => {
  return (
    <div className="w-full">
      {/* Desktop Timeline - Horizontal */}
      <div className="hidden md:block">
        <div className="relative max-w-5xl mx-auto">
          {/* Timeline line */}
          <div className="absolute top-1/2 left-16 right-16 h-0.5 bg-vintage-gold/30 transform -translate-y-1/2" />

          <motion.div
            className="flex items-center justify-center space-x-8 px-4 relative z-10"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {photos.map((photo, index) => (
              <motion.div
                key={photo.id}
                className="flex flex-col items-center"
                variants={staggerItem}
              >
                {/* Photo thumbnail */}
                <motion.div
                  className={`relative cursor-pointer transition-all duration-300 ${
                    index === currentIndex
                      ? 'w-32 h-32 golden-glow'
                      : 'w-24 h-24 hover:scale-105'
                  }`}
                  onClick={() => photo.isUnlocked && onPhotoSelect(index)}
                  whileHover={photo.isUnlocked ? { scale: 1.05 } : {}}
                  whileTap={photo.isUnlocked ? { scale: 0.95 } : {}}
                >
                  <div className={`
                    relative w-full h-full rounded-lg overflow-hidden vintage-frame
                    ${!photo.isUnlocked ? 'opacity-50' : ''}
                    ${index === currentIndex ? 'ring-4 ring-vintage-gold' : ''}
                  `}>
                    <Image
                      src={photo.src}
                      alt={photo.alt}
                      fill
                      className={`object-cover ${!photo.isUnlocked ? 'blur-sm' : ''}`}
                      sizes="(max-width: 768px) 96px, 128px"
                    />

                    {/* Lock/Unlock overlay */}
                    {!photo.isUnlocked ? (
                      <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                        <Lock className="w-6 h-6 text-white drop-shadow-lg" />
                      </div>
                    ) : index < currentIndex && (
                      <motion.div
                        className="absolute top-2 right-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <CheckCircle className="w-5 h-5 text-vintage-gold drop-shadow-lg" />
                      </motion.div>
                    )}

                    {/* Progress indicator */}
                    <div className="absolute bottom-2 left-2 right-2">
                      <div className="w-full h-1 bg-white/30 rounded-full overflow-hidden">
                        <motion.div
                          className="h-full bg-vintage-gold"
                          initial={{ width: 0 }}
                          animate={{
                            width: photo.isUnlocked ? '100%' : '0%'
                          }}
                          transition={{ duration: 0.5, delay: 0.2 }}
                        />
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Photo metadata */}
                <div className="mt-4 text-center max-w-[120px]">
                  <p className="font-kalam text-sm text-vintage-brown font-medium">
                    {photo.date}
                  </p>
                  {photo.location && (
                    <p className="font-inter text-xs text-vintage-brown/60 truncate">
                      {photo.location}
                    </p>
                  )}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>

      {/* Mobile Timeline - Horizontal Scroll */}
      <div className="md:hidden">
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute top-1/2 left-8 right-8 h-0.5 bg-vintage-gold/30 transform -translate-y-1/2" />

          <div className="flex items-center justify-center space-x-4 overflow-x-auto px-6 py-4 relative z-10">
            {photos.map((photo, index) => (
              <motion.div
                key={photo.id}
                className="flex flex-col items-center flex-shrink-0"
                variants={staggerItem}
              >
                {/* Photo thumbnail */}
                <motion.div
                  className={`relative cursor-pointer transition-all duration-300 ${
                    index === currentIndex
                      ? 'w-24 h-24 golden-glow'
                      : 'w-20 h-20'
                  }`}
                  onClick={() => photo.isUnlocked && onPhotoSelect(index)}
                  whileTap={photo.isUnlocked ? { scale: 0.95 } : {}}
                >
                  <div className={`
                    relative w-full h-full rounded-lg overflow-hidden vintage-frame
                    ${!photo.isUnlocked ? 'opacity-50' : ''}
                    ${index === currentIndex ? 'ring-2 ring-vintage-gold' : ''}
                  `}>
                    <Image
                      src={photo.src}
                      alt={photo.alt}
                      fill
                      className={`object-cover ${!photo.isUnlocked ? 'blur-sm' : ''}`}
                      sizes="96px"
                    />

                    {!photo.isUnlocked ? (
                      <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                        <Lock className="w-4 h-4 text-white drop-shadow-lg" />
                      </div>
                    ) : index < currentIndex && (
                      <motion.div
                        className="absolute top-1 right-1"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <CheckCircle className="w-4 h-4 text-vintage-gold drop-shadow-lg" />
                      </motion.div>
                    )}
                  </div>
                </motion.div>

                {/* Photo info */}
                <div className="mt-2 text-center max-w-[80px]">
                  <p className="font-kalam text-xs text-vintage-brown font-medium">
                    {photo.date}
                  </p>
                  {photo.location && (
                    <p className="font-inter text-xs text-vintage-brown/60 truncate">
                      {photo.location}
                    </p>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Timeline;
