import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { AppState, Photo, VideoGeneration } from '@/types';

interface AppStore extends AppState {
  // Actions
  setCurrentPhoto: (index: number) => void;
  unlockNextPhoto: () => void;
  addVideoGeneration: (generation: VideoGeneration) => void;
  updateVideoGeneration: (id: string, updates: Partial<VideoGeneration>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | undefined) => void;
  initializePhotos: (photos: Photo[]) => void;
  resetProgress: () => void;
  getVideoForPhoto: (photoId: string) => VideoGeneration | undefined;
  getCompletedVideos: () => VideoGeneration[];
  getTotalProgress: () => number;
}

// Sample data - in a real app, this would come from an API
const samplePhotos: Photo[] = [
  {
    id: '1',
    src: '/photos/photo_1.jpg',
    alt: 'First childhood memory',
    date: 'Summer 2010',
    location: 'Grandma\'s backyard',
    isUnlocked: true,
    contextStory: 'Remember this day? We were playing in <PERSON>\'s garden, and you had just discovered that butterfly. You were so excited, jumping up and down, trying to catch it with your tiny hands.',
    brotherSignature: '- Your loving brother ❤️'
  },
  {
    id: '2',
    src: '/photos/photo_2.jpg',
    alt: 'Second childhood memory',
    date: 'Winter 2011',
    location: 'Living room',
    isUnlocked: false,
    contextStory: 'This was Christmas morning. You had just opened your favorite present - that stuffed elephant you carried everywhere for years. The look of pure joy on your face was priceless.',
    brotherSignature: '- Your loving brother ❤️'
  },
  {
    id: '3',
    src: '/photos/photo_3.jpg',
    alt: 'Third childhood memory',
    date: 'Spring 2012',
    location: 'Local park',
    isUnlocked: false,
    contextStory: 'Our first bike ride together! You were so determined to keep up with me, even though your legs could barely reach the pedals. You never gave up.',
    brotherSignature: '- Your loving brother ❤️'
  },
  {
    id: '4',
    src: '/photos/photo_4.jpg',
    alt: 'Fourth childhood memory',
    date: 'Summer 2013',
    location: 'Beach vacation',
    isUnlocked: false,
    contextStory: 'Building sandcastles at the beach. You insisted on making the tallest tower, and when the wave knocked it down, you just laughed and started building again.',
    brotherSignature: '- Your loving brother ❤️'
  },
  {
    id: '5',
    src: '/photos/photo_5.jpg',
    alt: 'Fifth childhood memory',
    date: 'Fall 2014',
    location: 'School playground',
    isUnlocked: false,
    contextStory: 'Your first day of school. You were nervous but trying to be brave. I promised I\'d pick you up, and you made me pinky promise. You still remember that, don\'t you?',
    brotherSignature: '- Your loving brother ❤️'
  }
];

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // Initial state
      photos: samplePhotos,
      currentPhotoIndex: 0,
      videoGenerations: [],
      isLoading: false,
      error: undefined,

  // Actions
  setCurrentPhoto: (index: number) => {
    const { photos } = get();
    if (index >= 0 && index < photos.length && photos[index].isUnlocked) {
      set({ currentPhotoIndex: index });
    }
  },

  unlockNextPhoto: () => {
    set((state) => {
      const nextIndex = state.currentPhotoIndex + 1;
      if (nextIndex < state.photos.length) {
        const updatedPhotos = state.photos.map((photo, index) => 
          index === nextIndex ? { ...photo, isUnlocked: true } : photo
        );
        return {
          photos: updatedPhotos,
          currentPhotoIndex: nextIndex
        };
      }
      return state;
    });
  },

  addVideoGeneration: (generation: VideoGeneration) => {
    set((state) => ({
      videoGenerations: [...state.videoGenerations, generation]
    }));
  },

  updateVideoGeneration: (id: string, updates: Partial<VideoGeneration>) => {
    set((state) => ({
      videoGenerations: state.videoGenerations.map(gen =>
        gen.id === id ? { ...gen, ...updates } : gen
      )
    }));
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | undefined) => {
    set({ error });
  },

  initializePhotos: (photos: Photo[]) => {
    set({ photos, currentPhotoIndex: 0 });
  },

  resetProgress: () => {
    set((state) => ({
      photos: state.photos.map((photo, index) => ({
        ...photo,
        isUnlocked: index === 0 // Only first photo unlocked
      })),
      currentPhotoIndex: 0,
      videoGenerations: [],
      error: undefined
    }));
  },

  getVideoForPhoto: (photoId: string) => {
    const { videoGenerations } = get();
    return videoGenerations.find(gen => gen.photoId === photoId && gen.status === 'completed');
  },

  getCompletedVideos: () => {
    const { videoGenerations } = get();
    return videoGenerations.filter(gen => gen.status === 'completed');
  },

  getTotalProgress: () => {
    const { photos } = get();
    const unlockedCount = photos.filter(photo => photo.isUnlocked).length;
    return Math.round((unlockedCount / photos.length) * 100);
  }
}),
{
  name: 'moments-forward-storage',
  storage: createJSONStorage(() => localStorage),
  partialize: (state) => ({
    photos: state.photos,
    currentPhotoIndex: state.currentPhotoIndex,
    videoGenerations: state.videoGenerations,
  }),
}
));
