{"framework": "nextjs", "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "outputDirectory": ".next", "env": {"RUNWAY_API_KEY": "@runway-api-key", "RUNWAY_BASE_URL": "@runway-base-url", "NEXT_PUBLIC_BASE_URL": "@next-public-base-url"}, "build": {"env": {"RUNWAY_API_KEY": "@runway-api-key", "RUNWAY_BASE_URL": "@runway-base-url", "NEXT_PUBLIC_BASE_URL": "@next-public-base-url"}}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}]}