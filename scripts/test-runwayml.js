#!/usr/bin/env node

/**
 * Simple integration test script for RunwayML API
 * This script tests the RunwayML integration without requiring a real API key
 */

const { createRunwayMLService, prepareImageForRunway, optimizePromptForVideo } = require('../src/lib/runwayml');

async function testRunwayMLIntegration() {
  console.log('🧪 Testing RunwayML Integration...\n');

  try {
    // Test 1: Service creation (should use mock service without API key)
    console.log('1. Testing service creation...');
    const service = createRunwayMLService();
    console.log('✅ Service created successfully (using mock service)');

    // Test 2: Image URL preparation
    console.log('\n2. Testing image URL preparation...');
    const testImageUrl = '/photos/photo1.svg';
    const preparedUrl = await prepareImageForRunway(testImageUrl);
    console.log(`✅ Image URL prepared: ${preparedUrl}`);

    // Test 3: Prompt optimization
    console.log('\n3. Testing prompt optimization...');
    const originalPrompt = 'A beautiful landscape';
    const optimizedPrompt = optimizePromptForVideo(originalPrompt);
    console.log(`✅ Prompt optimized: "${originalPrompt}" → "${optimizedPrompt}"`);

    // Test 4: Mock video generation
    console.log('\n4. Testing mock video generation...');
    const mockRequest = {
      model: 'gen4_turbo',
      promptImage: preparedUrl,
      promptText: optimizedPrompt,
      duration: 5,
      ratio: '1280:720',
    };

    const result = await service.generateVideoAndWait(mockRequest);
    console.log('✅ Mock video generation completed:');
    console.log(`   - Task ID: ${result.id}`);
    console.log(`   - Status: ${result.status}`);
    console.log(`   - Video URL: ${result.videoUrl}`);

    // Test 5: Status checking
    console.log('\n5. Testing status checking...');
    const statusResult = await service.getGenerationStatus(result.id);
    console.log('✅ Status check completed:');
    console.log(`   - Status: ${statusResult.status}`);
    console.log(`   - Video URL: ${statusResult.videoUrl}`);

    console.log('\n🎉 All tests passed! RunwayML integration is working correctly.');
    console.log('\n📝 Next steps:');
    console.log('   1. Add your RUNWAY_API_KEY to .env.local to use the real API');
    console.log('   2. Test with actual images and prompts');
    console.log('   3. Monitor generation progress and handle errors');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testRunwayMLIntegration();
