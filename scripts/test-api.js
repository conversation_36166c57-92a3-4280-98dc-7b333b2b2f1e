#!/usr/bin/env node

/**
 * Test script to verify the API endpoint works with image conversion
 */

async function testVideoGenerationAPI() {
  console.log('🧪 Testing Video Generation API...\n');
  
  try {
    const response = await fetch('http://localhost:3001/api/generate-video', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imageUrl: '/photos/photo_1.jpg',
        prompt: 'A beautiful family moment with gentle movement and warm lighting',
        duration: 5,
        ratio: '1280:720',
      }),
    });

    console.log(`Response status: ${response.status}`);
    
    const data = await response.json();
    console.log('Response data:', JSON.stringify(data, null, 2));

    if (response.ok && data.success) {
      console.log('\n✅ API test passed!');
      console.log(`   - Generation ID: ${data.generationId}`);
      console.log(`   - Status: ${data.status}`);
      console.log(`   - Video URL: ${data.videoUrl}`);
    } else {
      console.log('\n❌ API test failed');
      console.log(`   - Error: ${data.error}`);
    }

  } catch (error) {
    console.error('\n❌ API test failed with error:', error.message);
  }
}

// Run the test
testVideoGenerationAPI();
