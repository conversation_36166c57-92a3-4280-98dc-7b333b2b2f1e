#!/usr/bin/env node

/**
 * Test script to verify image conversion to data URI works correctly
 */

const fs = require('fs');
const path = require('path');

function getMimeType(extension) {
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.svg': 'image/svg+xml',
  };
  
  return mimeTypes[extension] || 'image/jpeg';
}

async function convertImageToDataUri(imagePath) {
  try {
    const fullPath = path.join(process.cwd(), 'public', imagePath);
    console.log(`Converting image: ${fullPath}`);
    
    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      throw new Error(`File not found: ${fullPath}`);
    }
    
    // Read file
    const fileBuffer = fs.readFileSync(fullPath);
    const base64 = fileBuffer.toString('base64');
    
    // Get MIME type
    const ext = path.extname(fullPath).toLowerCase();
    const mimeType = getMimeType(ext);
    
    // Create data URI
    const dataUri = `data:${mimeType};base64,${base64}`;
    
    console.log(`✅ Successfully converted to data URI`);
    console.log(`   - File size: ${fileBuffer.length} bytes`);
    console.log(`   - Base64 size: ${base64.length} characters`);
    console.log(`   - MIME type: ${mimeType}`);
    console.log(`   - Data URI length: ${dataUri.length} characters`);
    console.log(`   - Data URI preview: ${dataUri.substring(0, 100)}...`);
    
    return dataUri;
    
  } catch (error) {
    console.error(`❌ Failed to convert image: ${error.message}`);
    throw error;
  }
}

async function testImageConversion() {
  console.log('🧪 Testing Image Conversion to Data URI...\n');
  
  try {
    // Test with the first photo
    const imagePath = '/photos/photo_1.jpg';
    const dataUri = await convertImageToDataUri(imagePath);
    
    // Verify it's a valid data URI
    if (!dataUri.startsWith('data:image/')) {
      throw new Error('Invalid data URI format');
    }
    
    console.log('\n🎉 Image conversion test passed!');
    console.log('\n📝 This data URI can now be sent to RunwayML API');
    
  } catch (error) {
    console.error('\n❌ Image conversion test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testImageConversion();
